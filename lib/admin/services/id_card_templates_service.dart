import '../models/id_card_model.dart';

/// Service for managing ID card templates
/// 
/// This service provides:
/// - Pre-defined ID card templates with different designs
/// - Template customization capabilities
/// - Default institution information
class IDCardTemplatesService {
  /// Get all available ID card templates
  static List<IDCardTemplate> getAllTemplates() {
    return [
      _getModernTemplate(),
      _getClassicTemplate(),
      _getMinimalTemplate(),
      _getCorporateTemplate(),
      _getCreativeTemplate(),
    ];
  }

  /// Get template by ID
  static IDCardTemplate? getTemplateById(String id) {
    try {
      return getAllTemplates().firstWhere((template) => template.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get default institution information
  static InstitutionInfo getDefaultInstitutionInfo() {
    return const InstitutionInfo(
      name: 'Passion Learning Institute',
      logoUrl: 'assets/images/institution_logo.png',
      address: '123 Education Street, Learning City, LC 12345',
      website: 'www.passionlearning.edu',
      phone: '+****************',
      email: '<EMAIL>',
    );
  }

  /// Modern template with gradient background and clean design
  static IDCardTemplate _getModernTemplate() {
    return const IDCardTemplate(
      id: 'modern_001',
      name: 'Modern Blue',
      description: 'Modern design with blue gradient background and clean typography',
      designType: IDCardDesignType.modern,
      primaryColor: '#2196F3',
      secondaryColor: '#1976D2',
      backgroundAsset: 'assets/templates/modern_bg.svg',
      logoSpec: LogoSpecification(
        x: 10,
        y: 10,
        width: 40,
        height: 40,
      ),
      photoSpec: PhotoSpecification(
        x: 15,
        y: 60,
        width: 35,
        height: 45,
        isCircular: false,
      ),
      qrSpec: QRCodeSpecification(
        x: 60,
        y: 60,
        size: 25,
      ),
      textLayout: TextLayoutSpecification(
        nameX: 15,
        nameY: 110,
        nameFontSize: 12,
        idX: 15,
        idY: 125,
        idFontSize: 10,
        programX: 15,
        programY: 140,
        programFontSize: 9,
      ),
      dimensions: CardDimensions(
        width: 85.6,
        height: 53.98,
        unit: 'mm',
      ),
    );
  }

  /// Classic template with traditional academic design
  static IDCardTemplate _getClassicTemplate() {
    return const IDCardTemplate(
      id: 'classic_001',
      name: 'Classic Academic',
      description: 'Traditional academic design with formal layout and typography',
      designType: IDCardDesignType.classic,
      primaryColor: '#8B4513',
      secondaryColor: '#A0522D',
      backgroundAsset: 'assets/templates/classic_bg.svg',
      logoSpec: LogoSpecification(
        x: 5,
        y: 5,
        width: 35,
        height: 35,
      ),
      photoSpec: PhotoSpecification(
        x: 10,
        y: 50,
        width: 30,
        height: 40,
        isCircular: false,
      ),
      qrSpec: QRCodeSpecification(
        x: 55,
        y: 65,
        size: 20,
      ),
      textLayout: TextLayoutSpecification(
        nameX: 10,
        nameY: 95,
        nameFontSize: 11,
        idX: 10,
        idY: 108,
        idFontSize: 9,
        programX: 10,
        programY: 120,
        programFontSize: 8,
      ),
      dimensions: CardDimensions(
        width: 85.6,
        height: 53.98,
        unit: 'mm',
      ),
    );
  }

  /// Minimal template with clean, simple design
  static IDCardTemplate _getMinimalTemplate() {
    return const IDCardTemplate(
      id: 'minimal_001',
      name: 'Minimal White',
      description: 'Clean and minimal design with white background and subtle accents',
      designType: IDCardDesignType.minimal,
      primaryColor: '#424242',
      secondaryColor: '#757575',
      backgroundAsset: null,
      logoSpec: LogoSpecification(
        x: 8,
        y: 8,
        width: 30,
        height: 30,
      ),
      photoSpec: PhotoSpecification(
        x: 12,
        y: 45,
        width: 28,
        height: 35,
        isCircular: true,
      ),
      qrSpec: QRCodeSpecification(
        x: 50,
        y: 50,
        size: 22,
      ),
      textLayout: TextLayoutSpecification(
        nameX: 12,
        nameY: 85,
        nameFontSize: 10,
        idX: 12,
        idY: 97,
        idFontSize: 8,
        programX: 12,
        programY: 108,
        programFontSize: 7,
      ),
      dimensions: CardDimensions(
        width: 85.6,
        height: 53.98,
        unit: 'mm',
      ),
    );
  }

  /// Corporate template with professional business design
  static IDCardTemplate _getCorporateTemplate() {
    return const IDCardTemplate(
      id: 'corporate_001',
      name: 'Corporate Professional',
      description: 'Professional corporate design with structured layout',
      designType: IDCardDesignType.corporate,
      primaryColor: '#1565C0',
      secondaryColor: '#0D47A1',
      backgroundAsset: 'assets/templates/corporate_bg.svg',
      logoSpec: LogoSpecification(
        x: 12,
        y: 8,
        width: 45,
        height: 25,
      ),
      photoSpec: PhotoSpecification(
        x: 8,
        y: 40,
        width: 32,
        height: 42,
        isCircular: false,
      ),
      qrSpec: QRCodeSpecification(
        x: 50,
        y: 55,
        size: 24,
      ),
      textLayout: TextLayoutSpecification(
        nameX: 8,
        nameY: 87,
        nameFontSize: 11,
        idX: 8,
        idY: 100,
        idFontSize: 9,
        programX: 8,
        programY: 112,
        programFontSize: 8,
      ),
      dimensions: CardDimensions(
        width: 85.6,
        height: 53.98,
        unit: 'mm',
      ),
    );
  }

  /// Creative template with artistic design elements
  static IDCardTemplate _getCreativeTemplate() {
    return const IDCardTemplate(
      id: 'creative_001',
      name: 'Creative Artistic',
      description: 'Creative design with artistic elements and vibrant colors',
      designType: IDCardDesignType.creative,
      primaryColor: '#E91E63',
      secondaryColor: '#AD1457',
      backgroundAsset: 'assets/templates/creative_bg.svg',
      logoSpec: LogoSpecification(
        x: 15,
        y: 12,
        width: 38,
        height: 38,
      ),
      photoSpec: PhotoSpecification(
        x: 18,
        y: 55,
        width: 30,
        height: 38,
        isCircular: true,
      ),
      qrSpec: QRCodeSpecification(
        x: 55,
        y: 70,
        size: 20,
      ),
      textLayout: TextLayoutSpecification(
        nameX: 18,
        nameY: 98,
        nameFontSize: 10,
        idX: 18,
        idY: 110,
        idFontSize: 8,
        programX: 18,
        programY: 122,
        programFontSize: 7,
      ),
      dimensions: CardDimensions(
        width: 85.6,
        height: 53.98,
        unit: 'mm',
      ),
    );
  }

  /// Create custom template with user specifications
  static IDCardTemplate createCustomTemplate({
    required String name,
    required String description,
    required IDCardDesignType designType,
    required String primaryColor,
    required String secondaryColor,
    String? backgroundAsset,
    LogoSpecification? logoSpec,
    PhotoSpecification? photoSpec,
    QRCodeSpecification? qrSpec,
    TextLayoutSpecification? textLayout,
    CardDimensions? dimensions,
  }) {
    return IDCardTemplate(
      id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      description: description,
      designType: designType,
      primaryColor: primaryColor,
      secondaryColor: secondaryColor,
      backgroundAsset: backgroundAsset,
      logoSpec: logoSpec ?? const LogoSpecification(x: 10, y: 10, width: 40, height: 40),
      photoSpec: photoSpec ?? const PhotoSpecification(x: 15, y: 60, width: 35, height: 45),
      qrSpec: qrSpec ?? const QRCodeSpecification(x: 60, y: 60, size: 25),
      textLayout: textLayout ?? const TextLayoutSpecification(
        nameX: 15, nameY: 110, nameFontSize: 12,
        idX: 15, idY: 125, idFontSize: 10,
        programX: 15, programY: 140, programFontSize: 9,
      ),
      dimensions: dimensions ?? const CardDimensions(width: 85.6, height: 53.98, unit: 'mm'),
    );
  }

  /// Get template preview colors for UI display
  static Map<String, List<String>> getTemplatePreviewColors() {
    return {
      'modern_001': ['#2196F3', '#1976D2'],
      'classic_001': ['#8B4513', '#A0522D'],
      'minimal_001': ['#424242', '#757575'],
      'corporate_001': ['#1565C0', '#0D47A1'],
      'creative_001': ['#E91E63', '#AD1457'],
    };
  }
}
