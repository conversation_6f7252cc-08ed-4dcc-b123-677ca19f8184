import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/student/student_bloc.dart';
import '../blocs/student/student_event.dart';
import '../blocs/student/student_state.dart';
import '../models/student_model.dart';
import '../repositories/student_repository.dart';
import '../utils/responsive_utils.dart';

/// Edit Student Screen for modifying existing student information
/// 
/// This screen provides:
/// - Pre-filled form with existing student data
/// - Comprehensive editing capabilities
/// - Real-time validation and error handling
/// - Beautiful responsive design with animations
/// - Integration with BLoC pattern for state management
class EditStudentScreen extends StatefulWidget {
  final Student student;

  const EditStudentScreen({
    super.key,
    required this.student,
  });

  @override
  State<EditStudentScreen> createState() => _EditStudentScreenState();
}

class _EditStudentScreenState extends State<EditStudentScreen>
    with TickerProviderStateMixin {
  
  /// StudentBloc instance
  late StudentBloc _studentBloc;
  
  /// Animation controller for page animations
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  /// Form key for validation
  final _formKey = GlobalKey<FormState>();
  
  /// Form controllers
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _majorController = TextEditingController();
  final _gradeLevelController = TextEditingController();
  final _gpaController = TextEditingController();
  final _creditsController = TextEditingController();
  
  /// Guardian info controllers
  final _guardianNameController = TextEditingController();
  final _guardianPhoneController = TextEditingController();
  final _guardianEmailController = TextEditingController();
  final _guardianRelationshipController = TextEditingController();
  
  /// Emergency contact controllers
  final _emergencyNameController = TextEditingController();
  final _emergencyPhoneController = TextEditingController();
  final _emergencyEmailController = TextEditingController();
  final _emergencyRelationshipController = TextEditingController();
  
  /// Form state variables
  DateTime? _selectedDateOfBirth;
  DateTime? _selectedExpectedGraduation;
  String? _selectedGender;
  StudentStatus _selectedStatus = StudentStatus.active;
  
  /// Loading state
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _studentBloc = StudentBloc(studentRepository: StudentRepository());
    _initializeAnimations();
    _populateFormFields();
  }

  /// Initialize animations for smooth UI transitions
  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  /// Populate form fields with existing student data
  void _populateFormFields() {
    final student = widget.student;
    
    _fullNameController.text = student.fullName;
    _emailController.text = student.email;
    _phoneController.text = student.phoneNumber ?? '';
    _addressController.text = student.address ?? '';
    _majorController.text = student.major ?? '';
    _gradeLevelController.text = student.gradeLevel ?? '';
    _gpaController.text = student.gpa?.toString() ?? '';
    _creditsController.text = student.creditsEarned?.toString() ?? '';
    
    _selectedDateOfBirth = student.dateOfBirth;
    _selectedExpectedGraduation = student.expectedGraduationDate;
    _selectedGender = student.gender;
    _selectedStatus = student.status;
    
    // Guardian info
    if (student.guardianInfo != null) {
      _guardianNameController.text = student.guardianInfo!.name;
      _guardianPhoneController.text = student.guardianInfo!.phoneNumber;
      _guardianEmailController.text = student.guardianInfo!.email ?? '';
      _guardianRelationshipController.text = student.guardianInfo!.relationship;
    }
    
    // Emergency contact
    if (student.emergencyContact != null) {
      _emergencyNameController.text = student.emergencyContact!.name;
      _emergencyPhoneController.text = student.emergencyContact!.phoneNumber;
      _emergencyEmailController.text = student.emergencyContact!.email ?? '';
      _emergencyRelationshipController.text = student.emergencyContact!.relationship;
    }
  }

  @override
  void dispose() {
    _studentBloc.close();
    _animationController.dispose();
    _disposeControllers();
    super.dispose();
  }

  /// Dispose all text controllers
  void _disposeControllers() {
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _majorController.dispose();
    _gradeLevelController.dispose();
    _gpaController.dispose();
    _creditsController.dispose();
    _guardianNameController.dispose();
    _guardianPhoneController.dispose();
    _guardianEmailController.dispose();
    _guardianRelationshipController.dispose();
    _emergencyNameController.dispose();
    _emergencyPhoneController.dispose();
    _emergencyEmailController.dispose();
    _emergencyRelationshipController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _studentBloc,
      child: BlocListener<StudentBloc, StudentState>(
        listener: _handleStateChanges,
        child: Scaffold(
          appBar: _buildAppBar(),
          body: _buildBody(),
        ),
      ),
    );
  }

  /// Handle BLoC state changes
  void _handleStateChanges(BuildContext context, StudentState state) {
    if (state is StudentUpdating) {
      setState(() {
        _isLoading = true;
      });
    } else if (state is StudentUpdated) {
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
      
      Navigator.pop(context, true); // Return with success flag
      
    } else if (state is StudentError) {
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${state.message}'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: _submitForm,
          ),
        ),
      );
    }
  }

  /// Build the app bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Edit Student',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      foregroundColor: Theme.of(context).primaryColor,
      actions: [
        // Save button
        TextButton(
          onPressed: _isLoading ? null : _submitForm,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text(
                  'Save',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
        ),
      ],
    );
  }

  /// Build the main body content
  Widget _buildBody() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: context.responsivePadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                _buildHeader(),
                const SizedBox(height: 24),
                
                // Form sections
                if (context.isLargeScreen) ...[
                  // Desktop layout - two columns
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 2,
                        child: Column(
                          children: [
                            _buildPersonalInfoSection(),
                            const SizedBox(height: 16),
                            _buildAcademicInfoSection(),
                          ],
                        ),
                      ),
                      const SizedBox(width: 24),
                      Expanded(
                        child: Column(
                          children: [
                            _buildGuardianInfoSection(),
                            const SizedBox(height: 16),
                            _buildEmergencyContactSection(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ] else ...[
                  // Mobile layout - single column
                  _buildPersonalInfoSection(),
                  const SizedBox(height: 16),
                  _buildAcademicInfoSection(),
                  const SizedBox(height: 16),
                  _buildGuardianInfoSection(),
                  const SizedBox(height: 16),
                  _buildEmergencyContactSection(),
                ],
                
                const SizedBox(height: 32),
                
                // Action buttons
                _buildActionButtons(),
                
                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build header section
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.orange.shade600,
            Colors.orange.shade400,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.edit,
            color: Colors.white,
            size: context.isLargeScreen ? 32 : 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Edit Student Information',
                  style: TextStyle(
                    fontSize: context.isLargeScreen ? 24 : 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Update ${widget.student.fullName}\'s details',
                  style: TextStyle(
                    fontSize: context.isLargeScreen ? 16 : 14,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
