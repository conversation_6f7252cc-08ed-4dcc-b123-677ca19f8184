import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/student/student_bloc.dart';
import '../blocs/student/student_event.dart';
import '../blocs/student/student_state.dart';
import '../models/student_model.dart';
import '../repositories/student_repository.dart';
import '../utils/responsive_utils.dart';

/// Create Student Screen for adding new students
/// 
/// This screen provides:
/// - Comprehensive student registration form
/// - Real-time validation and error handling
/// - Beautiful UI with animations and transitions
/// - Responsive design for all screen sizes
/// - Integration with Firebase through BLoC pattern
class CreateStudentScreen extends StatefulWidget {
  const CreateStudentScreen({super.key});

  @override
  State<CreateStudentScreen> createState() => _CreateStudentScreenState();
}

class _CreateStudentScreenState extends State<CreateStudentScreen>
    with TickerProviderStateMixin {
  
  /// StudentBloc instance
  late StudentBloc _studentBloc;
  
  /// Form key for validation
  final _formKey = GlobalKey<FormState>();
  
  /// Animation controller for form animations
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  /// Text editing controllers for form fields
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _profilePhotoUrlController = TextEditingController();
  final _gradeLevelController = TextEditingController();
  final _majorController = TextEditingController();
  final _academicYearController = TextEditingController();
  final _notesController = TextEditingController();

  /// Guardian information controllers
  final _guardianNameController = TextEditingController();
  final _guardianRelationshipController = TextEditingController();
  final _guardianPhoneController = TextEditingController();
  final _guardianEmailController = TextEditingController();
  final _guardianAddressController = TextEditingController();

  /// Emergency contact controllers
  final _emergencyNameController = TextEditingController();
  final _emergencyPhoneController = TextEditingController();
  final _emergencyRelationshipController = TextEditingController();
  final _emergencyEmailController = TextEditingController();

  /// Form state variables
  String _selectedGender = 'Male';
  StudentStatus _selectedStatus = StudentStatus.active;
  DateTime? _selectedDateOfBirth;
  DateTime _enrollmentDate = DateTime.now();
  bool _isActive = true;

  @override
  void initState() {
    super.initState();
    _studentBloc = BlocProvider.of<StudentBloc>(context);
    _initializeAnimations();
    _setDefaultValues();
  }

  /// Initialize animations for smooth UI transitions
  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  /// Set default values for form fields
  void _setDefaultValues() {
    _profilePhotoUrlController.text = 'https://via.placeholder.com/150x150?text=Student+Photo';
    _academicYearController.text = DateTime.now().year.toString();
  }

  @override
  void dispose() {
    // Dispose controllers to prevent memory leaks
    _animationController.dispose();
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _profilePhotoUrlController.dispose();
    _gradeLevelController.dispose();
    _majorController.dispose();
    _academicYearController.dispose();
    _notesController.dispose();
    _guardianNameController.dispose();
    _guardianRelationshipController.dispose();
    _guardianPhoneController.dispose();
    _guardianEmailController.dispose();
    _guardianAddressController.dispose();
    _emergencyNameController.dispose();
    _emergencyPhoneController.dispose();
    _emergencyRelationshipController.dispose();
    _emergencyEmailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<StudentBloc, StudentState>(
      listener: _handleStateChanges,
      child: Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(),
      ),
    );
  }

  /// Handle BLoC state changes
  void _handleStateChanges(BuildContext context, StudentState state) {
    // Debug logging for development
    if (state is StudentCreating) {
      // Student creation in progress
    } else if (state is StudentCreated) {
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 3),
        ),
      );

      // Navigate back with success result
      Navigator.pop(context, true);

    } else if (state is StudentError) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${state.message}'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: _submitForm,
          ),
        ),
      );
    }
  }

  /// Build the app bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Add New Student',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      foregroundColor: Theme.of(context).primaryColor,
      actions: [
        // Test Firebase connection button
        IconButton(
          onPressed: _testFirebaseConnection,
          icon: const Icon(Icons.wifi),
          tooltip: 'Test Firebase',
        ),
        
        // Reset form button
        IconButton(
          onPressed: _resetForm,
          icon: const Icon(Icons.refresh),
          tooltip: 'Reset Form',
        ),
      ],
    );
  }

  /// Build the main body content
  Widget _buildBody() {
    return BlocBuilder<StudentBloc, StudentState>(
      builder: (context, state) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: SingleChildScrollView(
              padding: context.responsivePadding,
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    _buildHeader(),
                    SizedBox(height: context.responsiveSectionSpacing),
                    
                    // Basic Information Section
                    _buildBasicInformationSection(),
                    SizedBox(height: context.responsiveSectionSpacing),
                    
                    // Contact Information Section
                    _buildContactInformationSection(),
                    SizedBox(height: context.responsiveSectionSpacing),
                    
                    // Academic Information Section
                    _buildAcademicInformationSection(),
                    SizedBox(height: context.responsiveSectionSpacing),
                    
                    // Guardian Information Section
                    _buildGuardianInformationSection(),
                    SizedBox(height: context.responsiveSectionSpacing),
                    
                    // Emergency Contact Section
                    _buildEmergencyContactSection(),
                    SizedBox(height: context.responsiveSectionSpacing),
                    
                    // Additional Information Section
                    _buildAdditionalInformationSection(),
                    SizedBox(height: context.responsiveSectionSpacing),
                    
                    // Action buttons
                    _buildActionButtons(state),
                    SizedBox(height: context.responsiveSectionSpacing),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Build header section
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Student Registration',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Fill in the information below to register a new student',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  /// Build basic information section
  Widget _buildBasicInformationSection() {
    return _buildSection(
      title: 'Basic Information',
      icon: Icons.person,
      children: [
        // Full name field
        _buildTextField(
          controller: _fullNameController,
          label: 'Full Name',
          hint: 'Enter student\'s full name',
          icon: Icons.person,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'Full name is required';
            }
            if (value!.length < 2) {
              return 'Name must be at least 2 characters';
            }
            return null;
          },
        ),

        SizedBox(height: context.responsiveFormFieldSpacing),

        // Email field
        _buildTextField(
          controller: _emailController,
          label: 'Email Address',
          hint: 'Enter email address',
          icon: Icons.email,
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'Email is required';
            }
            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
              return 'Enter a valid email address';
            }
            return null;
          },
        ),

        SizedBox(height: context.responsiveFormFieldSpacing),

        // Gender dropdown
        _buildDropdownField<String>(
          value: _selectedGender,
          label: 'Gender',
          icon: Icons.wc,
          items: ['Male', 'Female', 'Other', 'Prefer not to say']
              .map((gender) => DropdownMenuItem(
                    value: gender,
                    child: Text(gender),
                  ))
              .toList(),
          onChanged: (value) {
            setState(() {
              _selectedGender = value!;
            });
          },
        ),

        SizedBox(height: context.responsiveFormFieldSpacing),

        // Date of birth field
        _buildDateField(
          label: 'Date of Birth',
          hint: 'Select date of birth',
          icon: Icons.cake,
          selectedDate: _selectedDateOfBirth,
          onDateSelected: (date) {
            setState(() {
              _selectedDateOfBirth = date;
            });
          },
        ),

        SizedBox(height: context.responsiveFormFieldSpacing),

        // Profile photo URL field
        _buildTextField(
          controller: _profilePhotoUrlController,
          label: 'Profile Photo URL',
          hint: 'Enter profile photo URL',
          icon: Icons.image,
          validator: (value) {
            if (value?.isNotEmpty == true) {
              final uri = Uri.tryParse(value!);
              if (uri == null || !uri.hasScheme) {
                return 'Enter a valid URL';
              }
            }
            return null;
          },
        ),
      ],
    );
  }

  /// Build contact information section
  Widget _buildContactInformationSection() {
    return _buildSection(
      title: 'Contact Information',
      icon: Icons.contact_phone,
      children: [
        // Phone number field
        _buildTextField(
          controller: _phoneController,
          label: 'Phone Number',
          hint: 'Enter phone number',
          icon: Icons.phone,
          keyboardType: TextInputType.phone,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9+\-\s\(\)]')),
          ],
          validator: (value) {
            if (value?.isNotEmpty == true && value!.length < 10) {
              return 'Enter a valid phone number';
            }
            return null;
          },
        ),

        SizedBox(height: context.responsiveFormFieldSpacing),

        // Address field
        _buildTextField(
          controller: _addressController,
          label: 'Address',
          hint: 'Enter full address',
          icon: Icons.location_on,
          maxLines: 3,
        ),
      ],
    );
  }

  /// Build academic information section
  Widget _buildAcademicInformationSection() {
    return _buildSection(
      title: 'Academic Information',
      icon: Icons.school,
      children: [
        // Student status dropdown
        _buildDropdownField<StudentStatus>(
          value: _selectedStatus,
          label: 'Student Status',
          icon: Icons.assignment_ind,
          items: StudentStatus.values
              .map((status) => DropdownMenuItem(
                    value: status,
                    child: Text(status.displayName),
                  ))
              .toList(),
          onChanged: (value) {
            setState(() {
              _selectedStatus = value!;
            });
          },
        ),

        SizedBox(height: context.responsiveFormFieldSpacing),

        // Enrollment date field
        _buildDateField(
          label: 'Enrollment Date',
          hint: 'Select enrollment date',
          icon: Icons.calendar_today,
          selectedDate: _enrollmentDate,
          onDateSelected: (date) {
            setState(() {
              _enrollmentDate = date;
            });
          },
        ),

        SizedBox(height: context.responsiveFormFieldSpacing),

        // Grade level field
        _buildTextField(
          controller: _gradeLevelController,
          label: 'Grade Level',
          hint: 'e.g., Grade 10, Freshman',
          icon: Icons.grade,
        ),

        SizedBox(height: context.responsiveFormFieldSpacing),

        // Major field
        _buildTextField(
          controller: _majorController,
          label: 'Major/Specialization',
          hint: 'Enter major or specialization',
          icon: Icons.subject,
        ),

        SizedBox(height: context.responsiveFormFieldSpacing),

        // Academic year field
        _buildTextField(
          controller: _academicYearController,
          label: 'Academic Year',
          hint: 'Enter academic year',
          icon: Icons.calendar_today,
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        ),
      ],
    );
  }

  /// Build guardian information section
  Widget _buildGuardianInformationSection() {
    return _buildSection(
      title: 'Guardian Information',
      icon: Icons.family_restroom,
      children: [
        _buildTextField(
          controller: _guardianNameController,
          label: 'Guardian Name',
          hint: 'Enter guardian\'s full name',
          icon: Icons.person,
        ),

        SizedBox(height: context.responsiveFormFieldSpacing),

        _buildTextField(
          controller: _guardianRelationshipController,
          label: 'Relationship',
          hint: 'e.g., Father, Mother, Guardian',
          icon: Icons.family_restroom,
        ),

        SizedBox(height: context.responsiveFormFieldSpacing),

        _buildTextField(
          controller: _guardianPhoneController,
          label: 'Guardian Phone',
          hint: 'Enter guardian\'s phone number',
          icon: Icons.phone,
          keyboardType: TextInputType.phone,
        ),

        SizedBox(height: context.responsiveFormFieldSpacing),

        _buildTextField(
          controller: _guardianEmailController,
          label: 'Guardian Email',
          hint: 'Enter guardian\'s email',
          icon: Icons.email,
          keyboardType: TextInputType.emailAddress,
        ),
      ],
    );
  }

  /// Build emergency contact section
  Widget _buildEmergencyContactSection() {
    return _buildSection(
      title: 'Emergency Contact',
      icon: Icons.emergency,
      children: [
        _buildTextField(
          controller: _emergencyNameController,
          label: 'Emergency Contact Name',
          hint: 'Enter emergency contact name',
          icon: Icons.person,
        ),

        SizedBox(height: context.responsiveFormFieldSpacing),

        _buildTextField(
          controller: _emergencyPhoneController,
          label: 'Emergency Phone',
          hint: 'Enter emergency contact phone',
          icon: Icons.phone,
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value?.isNotEmpty == true && value!.length < 10) {
              return 'Enter a valid phone number';
            }
            return null;
          },
        ),

        SizedBox(height: context.responsiveFormFieldSpacing),

        _buildTextField(
          controller: _emergencyRelationshipController,
          label: 'Relationship',
          hint: 'Relationship to student',
          icon: Icons.family_restroom,
        ),
      ],
    );
  }

  /// Build additional information section
  Widget _buildAdditionalInformationSection() {
    return _buildSection(
      title: 'Additional Information',
      icon: Icons.note,
      children: [
        _buildTextField(
          controller: _notesController,
          label: 'Notes',
          hint: 'Any additional notes about the student',
          icon: Icons.note,
          maxLines: 4,
        ),

        SizedBox(height: context.responsiveFormFieldSpacing),

        // Active status switch
        SwitchListTile(
          title: const Text('Active Student'),
          subtitle: const Text('Whether this student account is active'),
          value: _isActive,
          onChanged: (value) {
            setState(() {
              _isActive = value;
            });
          },
          secondary: const Icon(Icons.toggle_on),
        ),
      ],
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(StudentState state) {
    final isLoading = state is StudentCreating;

    return Row(
      children: [
        // Cancel button
        Expanded(
          child: OutlinedButton(
            onPressed: isLoading ? null : () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: context.responsiveButtonHeight / 4),
            ),
            child: const Text('Cancel'),
          ),
        ),

        const SizedBox(width: 16),

        // Create button
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: isLoading ? null : _submitForm,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: context.responsiveButtonHeight / 4),
            ),
            child: isLoading
                ? const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                      SizedBox(width: 12),
                      Text('Creating Student...'),
                    ],
                  )
                : const Text('Create Student'),
          ),
        ),
      ],
    );
  }

  /// Build section container
  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade100,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              Icon(icon, color: Colors.blue.shade600),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Section content
          ...children,
        ],
      ),
    );
  }

  /// Build text field
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    int maxLines = 1,
  }) {
    return TextFormField(
      controller: controller,
      validator: validator,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),
    );
  }

  /// Build dropdown field
  Widget _buildDropdownField<T>({
    required T value,
    required String label,
    required IconData icon,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),
      items: items,
      onChanged: onChanged,
    );
  }

  /// Build date field
  Widget _buildDateField({
    required String label,
    required String hint,
    required IconData icon,
    required DateTime? selectedDate,
    required void Function(DateTime) onDateSelected,
  }) {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: selectedDate ?? DateTime.now(),
          firstDate: DateTime(1900),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        );
        if (date != null) {
          onDateSelected(date);
        }
      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixIcon: Icon(icon),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          filled: true,
          fillColor: Colors.grey.shade50,
        ),
        child: Text(
          selectedDate != null
              ? '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}'
              : hint,
          style: TextStyle(
            color: selectedDate != null ? Colors.black : Colors.grey.shade600,
          ),
        ),
      ),
    );
  }

  /// Submit the form and create student
  void _submitForm() {
    if (_formKey.currentState?.validate() ?? false) {
      try {
        // Create guardian info if provided
        GuardianInfo? guardianInfo;
        if (_guardianNameController.text.trim().isNotEmpty) {
          guardianInfo = GuardianInfo(
            name: _guardianNameController.text.trim(),
            relationship: _guardianRelationshipController.text.trim().isEmpty
                ? null
                : _guardianRelationshipController.text.trim(),
            phoneNumber: _guardianPhoneController.text.trim().isEmpty
                ? null
                : _guardianPhoneController.text.trim(),
            email: _guardianEmailController.text.trim().isEmpty
                ? null
                : _guardianEmailController.text.trim(),
            address: _guardianAddressController.text.trim().isEmpty
                ? null
                : _guardianAddressController.text.trim(),
          );
        }

        // Create emergency contact if provided
        EmergencyContact? emergencyContact;
        if (_emergencyNameController.text.trim().isNotEmpty &&
            _emergencyPhoneController.text.trim().isNotEmpty) {
          emergencyContact = EmergencyContact(
            name: _emergencyNameController.text.trim(),
            phoneNumber: _emergencyPhoneController.text.trim(),
            relationship: _emergencyRelationshipController.text.trim().isEmpty
                ? null
                : _emergencyRelationshipController.text.trim(),
            email: _emergencyEmailController.text.trim().isEmpty
                ? null
                : _emergencyEmailController.text.trim(),
          );
        }

        // Create student object with new model structure
        final student = Student(
          id: '', // Will be generated by Firebase
          studentId: '', // Will be generated by repository
          fullName: _fullNameController.text.trim(),
          email: _emailController.text.trim(),
          phoneNumber: _phoneController.text.trim().isEmpty
              ? null
              : _phoneController.text.trim(),
          address: _addressController.text.trim().isEmpty
              ? null
              : _addressController.text.trim(),
          dateOfBirth: _selectedDateOfBirth,
          gender: _selectedGender == 'Prefer not to say' ? null : _selectedGender,
          profilePhotoUrl: _profilePhotoUrlController.text.trim().isEmpty
              ? null
              : _profilePhotoUrlController.text.trim(),
          enrollmentDate: _enrollmentDate,
          status: _selectedStatus,
          gradeLevel: _gradeLevelController.text.trim().isEmpty
              ? null
              : _gradeLevelController.text.trim(),
          guardianInfo: guardianInfo,
          enrolledCourses: const [], // Empty for new student
          gpa: null, // No GPA initially
          totalCredits: 0,
          emergencyContact: emergencyContact,
          academicYear: _academicYearController.text.trim().isEmpty
              ? null
              : _academicYearController.text.trim(),
          major: _majorController.text.trim().isEmpty
              ? null
              : _majorController.text.trim(),
          isActive: _isActive,
          lastLoginAt: null,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          notes: _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
        );

        // Dispatch create student event
        _studentBloc.add(CreateStudentEvent(student));

      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating student: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in all required fields correctly'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  /// Reset form to initial state
  void _resetForm() {
    _formKey.currentState?.reset();

    // Clear all controllers
    _fullNameController.clear();
    _emailController.clear();
    _phoneController.clear();
    _addressController.clear();
    _gradeLevelController.clear();
    _majorController.clear();
    _notesController.clear();
    _guardianNameController.clear();
    _guardianRelationshipController.clear();
    _guardianPhoneController.clear();
    _guardianEmailController.clear();
    _guardianAddressController.clear();
    _emergencyNameController.clear();
    _emergencyPhoneController.clear();
    _emergencyRelationshipController.clear();
    _emergencyEmailController.clear();

    // Reset state variables
    setState(() {
      _selectedGender = 'Male';
      _selectedStatus = StudentStatus.active;
      _selectedDateOfBirth = null;
      _enrollmentDate = DateTime.now();
      _isActive = true;
    });

    // Set default values
    _setDefaultValues();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Form reset successfully'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// Test Firebase connection
  void _testFirebaseConnection() async {
    try {
      // Test creating a simple student
      final testStudent = Student(
        id: '',
        studentId: '',
        fullName: 'Test Student ${DateTime.now().millisecondsSinceEpoch}',
        email: 'test${DateTime.now().millisecondsSinceEpoch}@example.com',
        enrollmentDate: DateTime.now(),
        createdAt: DateTime.now(),
      );

      final repository = StudentRepository();
      final result = await repository.createStudent(testStudent);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Firebase test successful! Student ID: ${result.id}'),
            backgroundColor: Colors.green,
          ),
        );
      }

    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Firebase test failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
