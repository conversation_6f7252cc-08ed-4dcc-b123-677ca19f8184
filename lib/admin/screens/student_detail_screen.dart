import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/student/student_bloc.dart';
import '../blocs/student/student_event.dart';
import '../blocs/student/student_state.dart';
import '../models/student_model.dart';
import '../repositories/student_repository.dart';
import '../utils/responsive_utils.dart';
import 'edit_student_screen.dart';
import 'id_card_generation_screen.dart';

/// Student Detail Screen for viewing comprehensive student information
/// 
/// This screen provides:
/// - Complete student profile view
/// - Academic information and progress
/// - Contact and emergency information
/// - Action buttons for edit/delete operations
/// - Beautiful responsive design with animations
class StudentDetailScreen extends StatefulWidget {
  final Student student;

  const StudentDetailScreen({
    super.key,
    required this.student,
  });

  @override
  State<StudentDetailScreen> createState() => _StudentDetailScreenState();
}

class _StudentDetailScreenState extends State<StudentDetailScreen>
    with TickerProviderStateMixin {
  
  /// StudentBloc instance
  late StudentBloc _studentBloc;
  
  /// Animation controller for page animations
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  /// Current student data
  late Student _currentStudent;

  @override
  void initState() {
    super.initState();
    _currentStudent = widget.student;
    _studentBloc = StudentBloc(studentRepository: StudentRepository());
    _initializeAnimations();
  }

  /// Initialize animations for smooth UI transitions
  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _studentBloc.close();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _studentBloc,
      child: BlocListener<StudentBloc, StudentState>(
        listener: _handleStateChanges,
        child: Scaffold(
          appBar: _buildAppBar(),
          body: _buildBody(),
        ),
      ),
    );
  }

  /// Handle BLoC state changes
  void _handleStateChanges(BuildContext context, StudentState state) {
    if (state is StudentUpdated) {
      setState(() {
        _currentStudent = state.student;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } else if (state is StudentDeleted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
      Navigator.pop(context, true); // Return to list with refresh flag
    } else if (state is StudentError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${state.message}'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  /// Build the app bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        _currentStudent.fullName,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      foregroundColor: Theme.of(context).primaryColor,
      actions: [
        // Edit button
        IconButton(
          onPressed: _editStudent,
          icon: const Icon(Icons.edit),
          tooltip: 'Edit Student',
        ),
        
        // Delete button
        IconButton(
          onPressed: _deleteStudent,
          icon: const Icon(Icons.delete),
          tooltip: 'Delete Student',
        ),
        
        // More options
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('Share Profile'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download),
                  SizedBox(width: 8),
                  Text('Export Data'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'print',
              child: Row(
                children: [
                  Icon(Icons.print),
                  SizedBox(width: 8),
                  Text('Print Profile'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build the main body content
  Widget _buildBody() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          padding: context.responsivePadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile header
              _buildProfileHeader(),
              const SizedBox(height: 24),
              
              // Information sections
              if (context.isLargeScreen) ...[
                // Desktop layout - two columns
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Column(
                        children: [
                          _buildPersonalInfoSection(),
                          const SizedBox(height: 16),
                          _buildAcademicInfoSection(),
                        ],
                      ),
                    ),
                    const SizedBox(width: 24),
                    Expanded(
                      child: Column(
                        children: [
                          _buildContactInfoSection(),
                          const SizedBox(height: 16),
                          _buildEmergencyContactSection(),
                        ],
                      ),
                    ),
                  ],
                ),
              ] else ...[
                // Mobile layout - single column
                _buildPersonalInfoSection(),
                const SizedBox(height: 16),
                _buildAcademicInfoSection(),
                const SizedBox(height: 16),
                _buildContactInfoSection(),
                const SizedBox(height: 16),
                _buildEmergencyContactSection(),
              ],
              
              const SizedBox(height: 24),
              
              // Action buttons
              _buildActionButtons(),
              
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  /// Build profile header with photo and basic info
  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.shade600,
            Colors.blue.shade400,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // Profile photo
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 3),
            ),
            child: CircleAvatar(
              radius: context.isLargeScreen ? 50 : 40,
              backgroundImage: _currentStudent.profilePhotoUrl?.isNotEmpty == true
                  ? NetworkImage(_currentStudent.profilePhotoUrl!)
                  : null,
              child: _currentStudent.profilePhotoUrl?.isEmpty != false
                  ? Text(
                      _currentStudent.fullName.isNotEmpty
                          ? _currentStudent.fullName[0].toUpperCase()
                          : '?',
                      style: TextStyle(
                        fontSize: context.isLargeScreen ? 32 : 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    )
                  : null,
            ),
          ),
          
          const SizedBox(width: 20),
          
          // Basic info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _currentStudent.fullName,
                  style: TextStyle(
                    fontSize: context.isLargeScreen ? 28 : 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'ID: ${_currentStudent.studentId}',
                  style: TextStyle(
                    fontSize: context.isLargeScreen ? 16 : 14,
                    color: Colors.white.withOpacity(0.9),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                _buildStatusBadge(_currentStudent.status),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build status badge
  Widget _buildStatusBadge(StudentStatus status) {
    Color color;
    switch (status) {
      case StudentStatus.active:
        color = Colors.green;
        break;
      case StudentStatus.inactive:
        color = Colors.orange;
        break;
      case StudentStatus.suspended:
        color = Colors.red;
        break;
      case StudentStatus.graduated:
        color = Colors.blue;
        break;
      case StudentStatus.transferred:
        color = Colors.purple;
        break;
      case StudentStatus.dropped:
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Text(
        status.displayName,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Build personal information section
  Widget _buildPersonalInfoSection() {
    return _buildInfoCard(
      title: 'Personal Information',
      icon: Icons.person,
      children: [
        _buildInfoRow('Full Name', _currentStudent.fullName),
        _buildInfoRow('Email', _currentStudent.email),
        _buildInfoRow('Phone', _currentStudent.phoneNumber ?? 'Not provided'),
        _buildInfoRow('Date of Birth', _currentStudent.dateOfBirth != null
            ? '${_currentStudent.dateOfBirth!.day}/${_currentStudent.dateOfBirth!.month}/${_currentStudent.dateOfBirth!.year}'
            : 'Not provided'),
        _buildInfoRow('Gender', _currentStudent.gender ?? 'Not specified'),
        _buildInfoRow('Address', _currentStudent.address ?? 'Not provided'),
        _buildInfoRow('Enrollment Date',
            '${_currentStudent.enrollmentDate.day}/${_currentStudent.enrollmentDate.month}/${_currentStudent.enrollmentDate.year}'),
      ],
    );
  }

  /// Build academic information section
  Widget _buildAcademicInfoSection() {
    return _buildInfoCard(
      title: 'Academic Information',
      icon: Icons.school,
      children: [
        _buildInfoRow('Status', _currentStudent.status.displayName),
        _buildInfoRow('Grade Level', _currentStudent.gradeLevel ?? 'Not assigned'),
        _buildInfoRow('Major/Program', _currentStudent.major ?? 'Not specified'),
        _buildInfoRow('GPA', _currentStudent.gpa?.toStringAsFixed(2) ?? 'Not calculated'),
        _buildInfoRow('Credits Earned', _currentStudent.totalCredits.toString()),
      ],
    );
  }

  /// Build contact information section
  Widget _buildContactInfoSection() {
    return _buildInfoCard(
      title: 'Contact Information',
      icon: Icons.contact_phone,
      children: [
        _buildInfoRow('Email', _currentStudent.email),
        _buildInfoRow('Phone', _currentStudent.phoneNumber ?? 'Not provided'),
        _buildInfoRow('Address', _currentStudent.address ?? 'Not provided'),
        if (_currentStudent.guardianInfo != null) ...[
          const Divider(),
          Text(
            'Guardian Information',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          _buildInfoRow('Guardian Name', _currentStudent.guardianInfo!.name),
          _buildInfoRow('Relationship', _currentStudent.guardianInfo!.relationship ?? 'Not specified'),
          _buildInfoRow('Guardian Phone', _currentStudent.guardianInfo!.phoneNumber ?? 'Not provided'),
          _buildInfoRow('Guardian Email', _currentStudent.guardianInfo!.email ?? 'Not provided'),
        ],
      ],
    );
  }

  /// Build emergency contact section
  Widget _buildEmergencyContactSection() {
    return _buildInfoCard(
      title: 'Emergency Contact',
      icon: Icons.emergency,
      children: _currentStudent.emergencyContact != null
          ? [
              _buildInfoRow('Name', _currentStudent.emergencyContact!.name),
              _buildInfoRow('Relationship', _currentStudent.emergencyContact!.relationship ?? 'Not specified'),
              _buildInfoRow('Phone', _currentStudent.emergencyContact!.phoneNumber),
              _buildInfoRow('Email', _currentStudent.emergencyContact!.email ?? 'Not provided'),
            ]
          : [
              const Text(
                'No emergency contact information provided',
                style: TextStyle(
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
    );
  }

  /// Build information card wrapper
  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.blue.shade600, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  /// Build information row
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: context.isLargeScreen ? 140 : 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade600,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build action buttons
  Widget _buildActionButtons() {
    return Column(
      children: [
        // First row: Edit and Delete buttons
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _editStudent,
                icon: const Icon(Icons.edit),
                label: const Text('Edit Student'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _deleteStudent,
                icon: const Icon(Icons.delete),
                label: const Text('Delete Student'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                  side: const BorderSide(color: Colors.red),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Second row: Generate ID Card button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _generateIDCard,
            icon: const Icon(Icons.badge),
            label: const Text('Generate ID Card'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Edit student
  void _editStudent() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditStudentScreen(student: _currentStudent),
      ),
    );

    if (result == true) {
      // Refresh student data
      _studentBloc.add(GetStudentByIdEvent(_currentStudent.id));
    }
  }

  /// Delete student with confirmation
  void _deleteStudent() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Student'),
        content: Text(
          'Are you sure you want to delete ${_currentStudent.fullName}? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _studentBloc.add(DeleteStudentEvent(_currentStudent.id));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Handle menu actions
  void _handleMenuAction(String action) {
    switch (action) {
      case 'share':
        _shareProfile();
        break;
      case 'export':
        _exportData();
        break;
      case 'print':
        _printProfile();
        break;
    }
  }

  /// Share student profile
  void _shareProfile() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share feature coming soon!')),
    );
  }

  /// Export student data
  void _exportData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export feature coming soon!')),
    );
  }

  /// Print student profile
  void _printProfile() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Print feature coming soon!')),
    );
  }

  /// Generate ID card for student
  void _generateIDCard() async {
    try {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => BlocProvider.value(
            value: BlocProvider.of<StudentBloc>(this.context),
            child: IDCardGenerationScreen(student: _currentStudent),
          ),
        ),
      );

      if (result == true) {
        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('ID card generated successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error generating ID card: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
