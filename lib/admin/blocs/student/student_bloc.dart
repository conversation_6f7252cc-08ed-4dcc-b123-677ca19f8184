import 'package:flutter_bloc/flutter_bloc.dart';
import '../../repositories/student_repository.dart';
import '../../models/student_model.dart';
import '../../models/id_card_model.dart';
import '../../services/id_card_generation_service.dart';
import '../../services/id_card_templates_service.dart';
import 'student_event.dart';
import 'student_state.dart';

/// Student BLoC for managing student-related state and business logic
/// 
/// This BLoC handles:
/// - Student CRUD operations
/// - Search and filtering functionality
/// - Enrollment management
/// - State transitions and error handling
/// - Real-time data updates
class StudentBloc extends Bloc<StudentEvent, StudentState> {
  /// Student repository for Firebase operations
  final StudentRepository _studentRepository;

  /// Constructor initializes the BLoC with StudentInitial state
  StudentBloc({
    required StudentRepository studentRepository,
  }) : _studentRepository = studentRepository,
       super(const StudentInitial()) {
    
    // Register event handlers
    on<LoadStudentsEvent>(_onLoadStudents);
    on<RefreshStudentsEvent>(_onRefreshStudents);
    on<CreateStudentEvent>(_onCreateStudent);
    on<UpdateStudentEvent>(_onUpdateStudent);
    on<DeleteStudentEvent>(_onDeleteStudent);
    on<SearchStudentsEvent>(_onSearchStudents);
    on<ClearSearchEvent>(_onClearSearch);
    on<FilterStudentsByStatusEvent>(_onFilterStudentsByStatus);
    on<FilterStudentsByCourseEvent>(_onFilterStudentsByCourse);
    on<LoadActiveStudentsEvent>(_onLoadActiveStudents);
    on<LoadInactiveStudentsEvent>(_onLoadInactiveStudents);
    on<ToggleStudentStatusEvent>(_onToggleStudentStatus);
    on<EnrollStudentInCourseEvent>(_onEnrollStudentInCourse);
    on<UnenrollStudentFromCourseEvent>(_onUnenrollStudentFromCourse);
    on<GetStudentByIdEvent>(_onGetStudentById);
    on<UpdateStudentGPAEvent>(_onUpdateStudentGPA);
    on<UpdateStudentProfilePhotoEvent>(_onUpdateStudentProfilePhoto);
    on<BulkUpdateStudentsEvent>(_onBulkUpdateStudents);
    on<GenerateIDCardEvent>(_onGenerateIDCard);
    on<BatchGenerateIDCardsEvent>(_onBatchGenerateIDCards);
    on<PreviewIDCardEvent>(_onPreviewIDCard);
  }

  /// Handle loading all students
  Future<void> _onLoadStudents(
    LoadStudentsEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🎯 StudentBloc: Loading all students');
      emit(const StudentLoading());
      
      final students = await _studentRepository.getAllStudents();
      
      emit(StudentLoaded(students: students));
      print('✅ StudentBloc: Loaded ${students.length} students');
      
    } catch (e) {
      print('❌ StudentBloc: Error loading students: $e');
      emit(StudentError(
        message: 'Failed to load students: ${e.toString()}',
        errorCode: 'LOAD_STUDENTS_ERROR',
      ));
    }
  }

  /// Handle refreshing students data
  Future<void> _onRefreshStudents(
    RefreshStudentsEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🔄 StudentBloc: Refreshing students');
      
      final students = await _studentRepository.getAllStudents();
      
      emit(StudentLoaded(students: students));
      print('✅ StudentBloc: Refreshed ${students.length} students');
      
    } catch (e) {
      print('❌ StudentBloc: Error refreshing students: $e');
      emit(StudentError(
        message: 'Failed to refresh students: ${e.toString()}',
        previousState: state,
        errorCode: 'REFRESH_STUDENTS_ERROR',
      ));
    }
  }

  /// Handle creating a new student
  Future<void> _onCreateStudent(
    CreateStudentEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🎯 StudentBloc: Creating student: ${event.student.fullName}');
      emit(StudentCreating(event.student));
      
      final createdStudent = await _studentRepository.createStudent(event.student);
      
      emit(StudentCreated(student: createdStudent));
      print('✅ StudentBloc: Created student: ${createdStudent.fullName}');
      
      // Reload students to update the list
      add(const LoadStudentsEvent());
      
    } catch (e) {
      print('❌ StudentBloc: Error creating student: $e');
      emit(StudentError(
        message: 'Failed to create student: ${e.toString()}',
        previousState: state,
        errorCode: 'CREATE_STUDENT_ERROR',
      ));
    }
  }

  /// Handle updating an existing student
  Future<void> _onUpdateStudent(
    UpdateStudentEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🎯 StudentBloc: Updating student: ${event.student.fullName}');
      emit(StudentUpdating(event.student));
      
      final updatedStudent = await _studentRepository.updateStudent(event.student);
      
      emit(StudentUpdated(student: updatedStudent));
      print('✅ StudentBloc: Updated student: ${updatedStudent.fullName}');
      
      // Reload students to update the list
      add(const LoadStudentsEvent());
      
    } catch (e) {
      print('❌ StudentBloc: Error updating student: $e');
      emit(StudentError(
        message: 'Failed to update student: ${e.toString()}',
        previousState: state,
        errorCode: 'UPDATE_STUDENT_ERROR',
      ));
    }
  }

  /// Handle deleting a student
  Future<void> _onDeleteStudent(
    DeleteStudentEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🎯 StudentBloc: Deleting student: ${event.studentId}');
      emit(StudentDeleting(event.studentId));
      
      await _studentRepository.deleteStudent(event.studentId);
      
      emit(StudentDeleted(studentId: event.studentId));
      print('✅ StudentBloc: Deleted student: ${event.studentId}');
      
      // Reload students to update the list
      add(const LoadStudentsEvent());
      
    } catch (e) {
      print('❌ StudentBloc: Error deleting student: $e');
      emit(StudentError(
        message: 'Failed to delete student: ${e.toString()}',
        previousState: state,
        errorCode: 'DELETE_STUDENT_ERROR',
      ));
    }
  }

  /// Handle searching students
  Future<void> _onSearchStudents(
    SearchStudentsEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🔍 StudentBloc: Searching students with query: "${event.query}"');
      emit(StudentSearching(event.query));
      
      final students = await _studentRepository.searchStudents(event.query);
      
      emit(StudentLoaded(
        students: students,
        searchQuery: event.query,
        hasActiveFilters: event.query.isNotEmpty,
      ));
      print('✅ StudentBloc: Found ${students.length} students for query: "${event.query}"');
      
    } catch (e) {
      print('❌ StudentBloc: Error searching students: $e');
      emit(StudentError(
        message: 'Failed to search students: ${e.toString()}',
        previousState: state,
        errorCode: 'SEARCH_STUDENTS_ERROR',
      ));
    }
  }

  /// Handle clearing search
  Future<void> _onClearSearch(
    ClearSearchEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🔄 StudentBloc: Clearing search');
      
      final students = await _studentRepository.getAllStudents();
      
      emit(StudentLoaded(students: students));
      print('✅ StudentBloc: Search cleared, showing all students');
      
    } catch (e) {
      print('❌ StudentBloc: Error clearing search: $e');
      emit(StudentError(
        message: 'Failed to clear search: ${e.toString()}',
        previousState: state,
        errorCode: 'CLEAR_SEARCH_ERROR',
      ));
    }
  }

  /// Handle filtering students by status
  Future<void> _onFilterStudentsByStatus(
    FilterStudentsByStatusEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🔍 StudentBloc: Filtering students by status: ${event.status.displayName}');
      emit(const StudentLoading());
      
      final students = await _studentRepository.getStudentsByStatus(event.status);
      
      emit(StudentLoaded(
        students: students,
        filterCriteria: event.status.name,
        hasActiveFilters: true,
      ));
      print('✅ StudentBloc: Found ${students.length} students with status: ${event.status.displayName}');
      
    } catch (e) {
      print('❌ StudentBloc: Error filtering students by status: $e');
      emit(StudentError(
        message: 'Failed to filter students by status: ${e.toString()}',
        previousState: state,
        errorCode: 'FILTER_STUDENTS_BY_STATUS_ERROR',
      ));
    }
  }

  /// Handle filtering students by course
  Future<void> _onFilterStudentsByCourse(
    FilterStudentsByCourseEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🔍 StudentBloc: Filtering students by course: ${event.courseId}');
      emit(const StudentLoading());
      
      final students = await _studentRepository.getStudentsByCourse(event.courseId);
      
      emit(StudentLoaded(
        students: students,
        filterCriteria: 'course:${event.courseId}',
        hasActiveFilters: true,
      ));
      print('✅ StudentBloc: Found ${students.length} students in course: ${event.courseId}');
      
    } catch (e) {
      print('❌ StudentBloc: Error filtering students by course: $e');
      emit(StudentError(
        message: 'Failed to filter students by course: ${e.toString()}',
        previousState: state,
        errorCode: 'FILTER_STUDENTS_BY_COURSE_ERROR',
      ));
    }
  }

  /// Handle loading active students
  Future<void> _onLoadActiveStudents(
    LoadActiveStudentsEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🔍 StudentBloc: Loading active students');
      emit(const StudentLoading());
      
      final students = await _studentRepository.getStudentsByStatus(StudentStatus.active);
      
      emit(StudentLoaded(
        students: students,
        filterCriteria: 'active',
        hasActiveFilters: true,
      ));
      print('✅ StudentBloc: Loaded ${students.length} active students');
      
    } catch (e) {
      print('❌ StudentBloc: Error loading active students: $e');
      emit(StudentError(
        message: 'Failed to load active students: ${e.toString()}',
        previousState: state,
        errorCode: 'LOAD_ACTIVE_STUDENTS_ERROR',
      ));
    }
  }

  /// Handle loading inactive students
  Future<void> _onLoadInactiveStudents(
    LoadInactiveStudentsEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🔍 StudentBloc: Loading inactive students');
      emit(const StudentLoading());
      
      // Get all students and filter for non-active statuses
      final allStudents = await _studentRepository.getAllStudents();
      final inactiveStudents = allStudents
          .where((student) => student.status != StudentStatus.active)
          .toList();
      
      emit(StudentLoaded(
        students: inactiveStudents,
        filterCriteria: 'inactive',
        hasActiveFilters: true,
      ));
      print('✅ StudentBloc: Loaded ${inactiveStudents.length} inactive students');
      
    } catch (e) {
      print('❌ StudentBloc: Error loading inactive students: $e');
      emit(StudentError(
        message: 'Failed to load inactive students: ${e.toString()}',
        previousState: state,
        errorCode: 'LOAD_INACTIVE_STUDENTS_ERROR',
      ));
    }
  }

  /// Handle toggling student status
  Future<void> _onToggleStudentStatus(
    ToggleStudentStatusEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🔄 StudentBloc: Toggling status for student: ${event.studentId}');

      // Get current student
      final student = await _studentRepository.getStudentById(event.studentId);
      if (student == null) {
        throw Exception('Student not found');
      }

      // Toggle status
      final newStatus = student.status == StudentStatus.active
          ? StudentStatus.inactive
          : StudentStatus.active;

      final updatedStudent = student.copyWith(
        status: newStatus,
        updatedAt: DateTime.now(),
      );

      await _studentRepository.updateStudent(updatedStudent);

      emit(StudentUpdated(
        student: updatedStudent,
        message: 'Student status updated to ${newStatus.displayName}',
      ));

      // Reload students
      add(const LoadStudentsEvent());

    } catch (e) {
      print('❌ StudentBloc: Error toggling student status: $e');
      emit(StudentError(
        message: 'Failed to toggle student status: ${e.toString()}',
        previousState: state,
        errorCode: 'TOGGLE_STUDENT_STATUS_ERROR',
      ));
    }
  }

  /// Handle getting student by ID
  Future<void> _onGetStudentById(
    GetStudentByIdEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🔍 StudentBloc: Getting student by ID: ${event.studentId}');
      emit(StudentDetailLoading(event.studentId));

      final student = await _studentRepository.getStudentById(event.studentId);

      if (student != null) {
        emit(StudentDetailLoaded(student));
        print('✅ StudentBloc: Loaded student: ${student.fullName}');
      } else {
        emit(StudentError(
          message: 'Student not found',
          errorCode: 'STUDENT_NOT_FOUND',
        ));
      }

    } catch (e) {
      print('❌ StudentBloc: Error getting student by ID: $e');
      emit(StudentError(
        message: 'Failed to get student: ${e.toString()}',
        previousState: state,
        errorCode: 'GET_STUDENT_BY_ID_ERROR',
      ));
    }
  }

  /// Handle enrolling student in course
  Future<void> _onEnrollStudentInCourse(
    EnrollStudentInCourseEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('📚 StudentBloc: Enrolling student ${event.studentId} in course ${event.courseId}');
      emit(StudentEnrollmentProcessing(
        studentId: event.studentId,
        courseId: event.courseId,
        isEnrollment: true,
      ));

      // Get current student
      final student = await _studentRepository.getStudentById(event.studentId);
      if (student == null) {
        throw Exception('Student not found');
      }

      // Add course to enrolled courses if not already enrolled
      final enrolledCourses = List<String>.from(student.enrolledCourses);
      if (!enrolledCourses.contains(event.courseId)) {
        enrolledCourses.add(event.courseId);

        final updatedStudent = student.copyWith(
          enrolledCourses: enrolledCourses,
          updatedAt: DateTime.now(),
        );

        await _studentRepository.updateStudent(updatedStudent);

        emit(StudentEnrollmentCompleted(
          studentId: event.studentId,
          courseId: event.courseId,
          wasEnrollment: true,
          message: 'Student enrolled in course successfully',
        ));

        // Reload students
        add(const LoadStudentsEvent());
      } else {
        emit(StudentError(
          message: 'Student is already enrolled in this course',
          previousState: state,
          errorCode: 'ALREADY_ENROLLED_ERROR',
        ));
      }

    } catch (e) {
      print('❌ StudentBloc: Error enrolling student in course: $e');
      emit(StudentError(
        message: 'Failed to enroll student in course: ${e.toString()}',
        previousState: state,
        errorCode: 'ENROLL_STUDENT_ERROR',
      ));
    }
  }

  /// Handle unenrolling student from course
  Future<void> _onUnenrollStudentFromCourse(
    UnenrollStudentFromCourseEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('📚 StudentBloc: Unenrolling student ${event.studentId} from course ${event.courseId}');
      emit(StudentEnrollmentProcessing(
        studentId: event.studentId,
        courseId: event.courseId,
        isEnrollment: false,
      ));

      // Get current student
      final student = await _studentRepository.getStudentById(event.studentId);
      if (student == null) {
        throw Exception('Student not found');
      }

      // Remove course from enrolled courses
      final enrolledCourses = List<String>.from(student.enrolledCourses);
      if (enrolledCourses.contains(event.courseId)) {
        enrolledCourses.remove(event.courseId);

        final updatedStudent = student.copyWith(
          enrolledCourses: enrolledCourses,
          updatedAt: DateTime.now(),
        );

        await _studentRepository.updateStudent(updatedStudent);

        emit(StudentEnrollmentCompleted(
          studentId: event.studentId,
          courseId: event.courseId,
          wasEnrollment: false,
          message: 'Student unenrolled from course successfully',
        ));

        // Reload students
        add(const LoadStudentsEvent());
      } else {
        emit(StudentError(
          message: 'Student is not enrolled in this course',
          previousState: state,
          errorCode: 'NOT_ENROLLED_ERROR',
        ));
      }

    } catch (e) {
      print('❌ StudentBloc: Error unenrolling student from course: $e');
      emit(StudentError(
        message: 'Failed to unenroll student from course: ${e.toString()}',
        previousState: state,
        errorCode: 'UNENROLL_STUDENT_ERROR',
      ));
    }
  }

  /// Handle updating student GPA
  Future<void> _onUpdateStudentGPA(
    UpdateStudentGPAEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('📊 StudentBloc: Updating GPA for student: ${event.studentId}');

      // Get current student
      final student = await _studentRepository.getStudentById(event.studentId);
      if (student == null) {
        throw Exception('Student not found');
      }

      final updatedStudent = student.copyWith(
        gpa: event.gpa,
        updatedAt: DateTime.now(),
      );

      await _studentRepository.updateStudent(updatedStudent);

      emit(StudentUpdated(
        student: updatedStudent,
        message: 'Student GPA updated successfully',
      ));

      // Reload students
      add(const LoadStudentsEvent());

    } catch (e) {
      print('❌ StudentBloc: Error updating student GPA: $e');
      emit(StudentError(
        message: 'Failed to update student GPA: ${e.toString()}',
        previousState: state,
        errorCode: 'UPDATE_STUDENT_GPA_ERROR',
      ));
    }
  }

  /// Handle updating student profile photo
  Future<void> _onUpdateStudentProfilePhoto(
    UpdateStudentProfilePhotoEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('📸 StudentBloc: Updating profile photo for student: ${event.studentId}');

      // Get current student
      final student = await _studentRepository.getStudentById(event.studentId);
      if (student == null) {
        throw Exception('Student not found');
      }

      final updatedStudent = student.copyWith(
        profilePhotoUrl: event.photoUrl,
        updatedAt: DateTime.now(),
      );

      await _studentRepository.updateStudent(updatedStudent);

      emit(StudentUpdated(
        student: updatedStudent,
        message: 'Student profile photo updated successfully',
      ));

      // Reload students
      add(const LoadStudentsEvent());

    } catch (e) {
      print('❌ StudentBloc: Error updating student profile photo: $e');
      emit(StudentError(
        message: 'Failed to update student profile photo: ${e.toString()}',
        previousState: state,
        errorCode: 'UPDATE_STUDENT_PHOTO_ERROR',
      ));
    }
  }

  /// Handle bulk updating students
  Future<void> _onBulkUpdateStudents(
    BulkUpdateStudentsEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🔄 StudentBloc: Bulk updating ${event.studentIds.length} students');
      emit(StudentBulkOperationProcessing(
        studentCount: event.studentIds.length,
        operationType: 'update',
      ));

      int successCount = 0;
      int errorCount = 0;

      for (final studentId in event.studentIds) {
        try {
          final student = await _studentRepository.getStudentById(studentId);
          if (student != null) {
            // Apply updates based on updateData
            Student updatedStudent = student;

            if (event.updateData.containsKey('status')) {
              final statusName = event.updateData['status'] as String;
              final status = StudentStatus.values.firstWhere(
                (s) => s.name == statusName,
                orElse: () => student.status,
              );
              updatedStudent = updatedStudent.copyWith(status: status);
            }

            if (event.updateData.containsKey('gradeLevel')) {
              updatedStudent = updatedStudent.copyWith(
                gradeLevel: event.updateData['gradeLevel'] as String,
              );
            }

            if (event.updateData.containsKey('academicYear')) {
              updatedStudent = updatedStudent.copyWith(
                academicYear: event.updateData['academicYear'] as String,
              );
            }

            updatedStudent = updatedStudent.copyWith(updatedAt: DateTime.now());
            await _studentRepository.updateStudent(updatedStudent);
            successCount++;
          }
        } catch (e) {
          print('❌ Error updating student $studentId: $e');
          errorCount++;
        }
      }

      emit(StudentBulkOperationCompleted(
        studentCount: successCount,
        operationType: 'update',
        message: 'Updated $successCount students successfully${errorCount > 0 ? ' ($errorCount failed)' : ''}',
      ));

      // Reload students
      add(const LoadStudentsEvent());

    } catch (e) {
      print('❌ StudentBloc: Error in bulk update: $e');
      emit(StudentError(
        message: 'Failed to bulk update students: ${e.toString()}',
        previousState: state,
        errorCode: 'BULK_UPDATE_STUDENTS_ERROR',
      ));
    }
  }

  /// Handle generating ID card for a student
  Future<void> _onGenerateIDCard(
    GenerateIDCardEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🆔 StudentBloc: Generating ID card for student: ${event.studentId}');
      emit(IDCardGenerating(
        studentId: event.studentId,
        template: event.template,
        exportFormat: event.exportFormat,
      ));

      // Get student data
      final student = await _studentRepository.getStudentById(event.studentId);
      if (student == null) {
        throw Exception('Student not found');
      }

      // Create ID card
      final institutionInfo = IDCardTemplatesService.getDefaultInstitutionInfo();
      final idCard = IDCard.fromStudent(
        student: student,
        template: event.template,
        institutionInfo: institutionInfo,
      );

      // Generate ID card file
      late File idCardFile;
      switch (event.exportFormat) {
        case ExportFormat.pdf:
          idCardFile = await IDCardGenerationService.generatePDF(
            idCard: idCard,
            customPath: event.customPath,
          );
          break;
        case ExportFormat.cdr:
          idCardFile = await IDCardGenerationService.generateCDR(
            idCard: idCard,
            customPath: event.customPath,
          );
          break;
        case ExportFormat.png:
          idCardFile = await IDCardGenerationService.generatePreviewImage(
            idCard: idCard,
            customPath: event.customPath,
          );
          break;
        case ExportFormat.svg:
          idCardFile = await IDCardGenerationService.generateCDR(
            idCard: idCard,
            customPath: event.customPath,
          );
          break;
      }

      emit(IDCardGenerated(
        idCardFile: idCardFile,
        studentId: event.studentId,
        template: event.template,
        exportFormat: event.exportFormat,
        message: 'ID card generated successfully for ${student.fullName}',
      ));

      print('✅ StudentBloc: ID card generated: ${idCardFile.path}');

    } catch (e) {
      print('❌ StudentBloc: Error generating ID card: $e');
      emit(StudentError(
        message: 'Failed to generate ID card: ${e.toString()}',
        previousState: state,
        errorCode: 'GENERATE_ID_CARD_ERROR',
      ));
    }
  }

  /// Handle batch generating ID cards for multiple students
  Future<void> _onBatchGenerateIDCards(
    BatchGenerateIDCardsEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('🆔 StudentBloc: Batch generating ID cards for ${event.studentIds.length} students');
      emit(BatchIDCardGenerating(
        totalStudents: event.studentIds.length,
        generatedCount: 0,
        template: event.template,
        exportFormat: event.exportFormat,
      ));

      // Get all students
      final students = <Student>[];
      for (final studentId in event.studentIds) {
        final student = await _studentRepository.getStudentById(studentId);
        if (student != null) {
          students.add(student);
        }
      }

      // Generate ID cards in batch
      final institutionInfo = IDCardTemplatesService.getDefaultInstitutionInfo();
      final idCardFiles = await IDCardGenerationService.batchGenerate(
        students: students,
        template: event.template,
        institutionInfo: institutionInfo,
        format: event.exportFormat,
        customPath: event.customPath,
      );

      final successCount = idCardFiles.length;
      final failureCount = event.studentIds.length - successCount;

      emit(BatchIDCardGenerated(
        idCardFiles: idCardFiles,
        successCount: successCount,
        failureCount: failureCount,
        template: event.template,
        exportFormat: event.exportFormat,
        message: 'Generated $successCount ID cards successfully${failureCount > 0 ? ' ($failureCount failed)' : ''}',
      ));

      print('✅ StudentBloc: Batch ID card generation completed: $successCount/${ event.studentIds.length}');

    } catch (e) {
      print('❌ StudentBloc: Error in batch ID card generation: $e');
      emit(StudentError(
        message: 'Failed to generate ID cards: ${e.toString()}',
        previousState: state,
        errorCode: 'BATCH_GENERATE_ID_CARDS_ERROR',
      ));
    }
  }

  /// Handle previewing ID card before generation
  Future<void> _onPreviewIDCard(
    PreviewIDCardEvent event,
    Emitter<StudentState> emit,
  ) async {
    try {
      print('👁️ StudentBloc: Generating ID card preview for student: ${event.studentId}');

      // Get student data
      final student = await _studentRepository.getStudentById(event.studentId);
      if (student == null) {
        throw Exception('Student not found');
      }

      // Create preview ID card
      final institutionInfo = IDCardTemplatesService.getDefaultInstitutionInfo();
      final previewCard = IDCard.fromStudent(
        student: student,
        template: event.template,
        institutionInfo: institutionInfo,
      );

      emit(IDCardPreviewReady(
        previewCard: previewCard,
        student: student,
      ));

      print('✅ StudentBloc: ID card preview ready for ${student.fullName}');

    } catch (e) {
      print('❌ StudentBloc: Error generating ID card preview: $e');
      emit(StudentError(
        message: 'Failed to generate ID card preview: ${e.toString()}',
        previousState: state,
        errorCode: 'PREVIEW_ID_CARD_ERROR',
      ));
    }
  }
}
